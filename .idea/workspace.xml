<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="976073a1-5073-4bbb-9875-3002feb44517" name="Changes" comment="[紧急]常用api示例代码" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2yf9WgiYZ6nHxmjv99VAWJDtZeI" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;SONARLINT_PRECOMMIT_ANALYSIS&quot;: &quot;false&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;20250619&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Documents/work/code/JRworkspace/anywallet/app/rest&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;configurable.group.editor&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/app/rest" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="976073a1-5073-4bbb-9875-3002feb44517" name="Changes" comment="" />
      <created>1750214793693</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750214793693</updated>
      <workItem from="1750214795262" duration="4541000" />
      <workItem from="1750226806404" duration="3452000" />
      <workItem from="1750300383907" duration="5367000" />
      <workItem from="1750658939034" duration="14000" />
    </task>
    <task id="LOCAL-00001" summary="[紧急]初始化fastmcp工具代码">
      <option name="closed" value="true" />
      <created>1750218960780</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1750218960780</updated>
    </task>
    <task id="LOCAL-00002" summary="[紧急]预设钱包订单和钱包用户MCP工具代码">
      <option name="closed" value="true" />
      <created>1750229750422</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1750229750422</updated>
    </task>
    <task id="LOCAL-00003" summary="[紧急]常用api示例代码">
      <option name="closed" value="true" />
      <created>1750304686136</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1750304686136</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="[紧急]初始化fastmcp工具代码" />
    <MESSAGE value="[紧急]预设钱包订单和钱包用户MCP工具代码" />
    <MESSAGE value="[紧急]常用api示例代码" />
    <option name="LAST_COMMIT_MESSAGE" value="[紧急]常用api示例代码" />
  </component>
  <component name="com.ke.global.state.CodeLink_Configuration">
    <option name="webViewProjectConfig">
      <WebViewProjectConfig>
        <option name="gitInfo">
          <list>
            <GitBaseInfo>
              <option name="branchName" value="20250619" />
              <option name="gitAddress" value="https://git.lianjia.com/bkjk/2a2c/anywallet.git" />
              <option name="gitId" value="d1e7c577120434270b4848eb2121eb39" />
            </GitBaseInfo>
          </list>
        </option>
        <option name="isMavenProject" value="false" />
        <option name="jdkVersion" value="21" />
        <option name="sideCarIDEInfo">
          <SideCarIDEInfo>
            <option name="allFiles">
              <set />
            </option>
            <option name="editorUrl" value="http://127.0.0.1:41100/" />
            <option name="openFiles">
              <set />
            </option>
            <option name="projectId" value="5a577ab9" />
            <option name="rootDirectory" value="$PROJECT_DIR$" />
            <option name="shell" value="/bin/bash" />
            <option name="sideCarPort" value="42424" />
            <option name="socketPort" value="41100" />
          </SideCarIDEInfo>
        </option>
      </WebViewProjectConfig>
    </option>
    <option name="httpUri" value="bkjk/2a2c/anywallet" />
  </component>
</project>